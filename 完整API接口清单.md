# 企业管理系统 - 完整API接口清单

## 基础信息
- **API基础路径**: `/api`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON

## 1. 认证模块 (Authentication)

### 1.1 用户登录
- **接口**: `POST /api/auth/signin`
- **入参**: 
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "accessToken": "string",
      "id": "number",
      "username": "string", 
      "email": "string",
      "roles": ["ROLE_ADMIN", "ROLE_MANAGER", "ROLE_USER"]
    }
  }
  ```

### 1.2 用户注册
- **接口**: `POST /api/auth/signup`
- **入参**:
  ```json
  {
    "username": "string",
    "email": "string",
    "password": "string"
  }
  ```
- **出参**:
  ```json
  {
    "success": true,
    "message": "注册成功"
  }
  ```

## 2. 用户管理模块 (User Management)

### 2.1 获取用户列表
- **接口**: `GET /api/users`
- **入参**: 
  - `page`: number (页码)
  - `size`: number (每页数量)
  - `username`: string (用户名搜索)
  - `email`: string (邮箱搜索)
  - `status`: string (状态筛选)
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "content": [
        {
          "id": "number",
          "username": "string",
          "email": "string",
          "roles": ["string"],
          "status": "ACTIVE|INACTIVE",
          "lastLogin": "datetime",
          "createdAt": "datetime"
        }
      ],
      "totalElements": "number",
      "totalPages": "number"
    }
  }
  ```

### 2.2 获取单个用户
- **接口**: `GET /api/users/{id}`
- **出参**: 用户详细信息

### 2.3 创建用户
- **接口**: `POST /api/users`
- **入参**:
  ```json
  {
    "username": "string",
    "email": "string", 
    "password": "string",
    "roles": ["string"]
  }
  ```

### 2.4 更新用户
- **接口**: `PUT /api/users/{id}`
- **入参**: 同创建用户

### 2.5 删除用户
- **接口**: `DELETE /api/users/{id}`

### 2.6 切换用户状态
- **接口**: `PUT /api/users/{id}/status`

## 3. 项目管理模块 (Project Management)

### 3.1 获取项目列表
- **接口**: `GET /api/projects`
- **入参**:
  - `page`: number
  - `size`: number
  - `name`: string (项目名称搜索)
  - `status`: string (状态筛选)
  - `startDate`: date
  - `endDate`: date
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "content": [
        {
          "id": "number",
          "name": "string",
          "description": "string",
          "status": "IN_PROGRESS|COMPLETED|PAUSED|CANCELLED",
          "budget": "number",
          "startDate": "date",
          "endDate": "date", 
          "progress": "number",
          "teamId": "number",
          "managerId": "number"
        }
      ],
      "totalElements": "number"
    }
  }
  ```

### 3.2 获取单个项目
- **接口**: `GET /api/projects/{id}`

### 3.3 创建项目
- **接口**: `POST /api/projects`
- **入参**:
  ```json
  {
    "name": "string",
    "description": "string",
    "budget": "number",
    "startDate": "date",
    "endDate": "date",
    "teamId": "number",
    "managerId": "number"
  }
  ```

### 3.4 更新项目
- **接口**: `PUT /api/projects/{id}`

### 3.5 删除项目
- **接口**: `DELETE /api/projects/{id}`

## 4. 团队管理模块 (Team Management)

### 4.1 获取团队列表
- **接口**: `GET /api/teams`
- **入参**:
  - `page`: number
  - `size`: number
  - `name`: string (团队名称搜索)
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "content": [
        {
          "id": "number",
          "name": "string",
          "description": "string",
          "leaderId": "number",
          "leaderName": "string",
          "memberCount": "number",
          "members": [
            {
              "id": "number",
              "username": "string"
            }
          ],
          "createdAt": "datetime"
        }
      ],
      "totalElements": "number"
    }
  }
  ```

### 4.2 创建团队
- **接口**: `POST /api/teams`
- **入参**:
  ```json
  {
    "name": "string",
    "description": "string",
    "leaderId": "number",
    "members": ["number"]
  }
  ```

### 4.3 更新团队
- **接口**: `PUT /api/teams/{id}`

### 4.4 删除团队
- **接口**: `DELETE /api/teams/{id}`

### 4.5 获取团队成员
- **接口**: `GET /api/teams/{id}/members`

## 5. 财务管理模块 (Financial Management)

### 5.1 获取财务记录列表
- **接口**: `GET /api/financial/records`
- **入参**:
  - `page`: number
  - `size`: number
  - `type`: string (INCOME|EXPENSE)
  - `category`: string
  - `startDate`: string (YYYY-MM)
  - `endDate`: string (YYYY-MM)
  - `description`: string
  - `status`: string (PENDING|APPROVED|REJECTED)
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "records": [
        {
          "id": "number",
          "type": "INCOME|EXPENSE",
          "category": "PROJECT_INCOME|OFFICE_SUPPLIES|TRAVEL|TRAINING|OTHER",
          "amount": "number",
          "description": "string",
          "date": "date",
          "status": "PENDING|APPROVED|REJECTED",
          "createdBy": "string",
          "createdAt": "datetime",
          "updatedAt": "datetime"
        }
      ],
      "pagination": {
        "page": "number",
        "size": "number", 
        "total": "number",
        "totalPages": "number"
      }
    }
  }
  ```

### 5.2 获取财务统计
- **接口**: `GET /api/financial/stats`
- **入参**:
  - `startDate`: date (可选)
  - `endDate`: date (可选)
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "totalIncome": "number",
      "totalExpense": "number", 
      "totalProfit": "number",
      "pendingCount": "number"
    }
  }
  ```

### 5.3 创建财务记录
- **接口**: `POST /api/financial/records`
- **入参**:
  ```json
  {
    "type": "INCOME|EXPENSE",
    "category": "string",
    "amount": "number",
    "description": "string",
    "date": "date"
  }
  ```

### 5.4 更新财务记录
- **接口**: `PUT /api/financial/records/{id}`

### 5.5 删除财务记录
- **接口**: `DELETE /api/financial/records/{id}`

### 5.6 审核财务记录
- **接口**: `PUT /api/financial/records/{id}/approve`
- **入参**:
  ```json
  {
    "action": "APPROVE|REJECT",
    "comment": "string"
  }
  ```

### 5.7 导出财务数据
- **接口**: `GET /api/financial/export`
- **入参**: 同查询列表参数
- **出参**: Excel文件流

## 6. 报表模块 (Reports)

### 6.1 获取仪表盘统计
- **接口**: `GET /api/dashboard/stats`
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "activeProjects": "number",
      "totalTeams": "number", 
      "monthlyIncome": "number",
      "totalUsers": "number"
    }
  }
  ```

### 6.2 获取项目报表数据
- **接口**: `GET /api/reports/projects`
- **入参**:
  - `startDate`: date
  - `endDate`: date
- **出参**:
  ```json
  {
    "success": true,
    "data": [
      {
        "project": "string",
        "team": "string",
        "progress": "number",
        "budget": "number",
        "spent": "number", 
        "status": "string",
        "deadline": "date"
      }
    ]
  }
  ```

### 6.3 获取财务图表数据
- **接口**: `GET /api/reports/financial/charts`
- **入参**:
  - `startDate`: date
  - `endDate`: date
- **出参**:
  ```json
  {
    "success": true,
    "data": {
      "incomeExpenseChart": {
        "months": ["string"],
        "income": ["number"],
        "expense": ["number"]
      },
      "categoryChart": [
        {
          "name": "string",
          "value": "number"
        }
      ]
    }
  }
  ```

## 7. 个人资料模块 (Profile)

### 7.1 获取个人资料
- **接口**: `GET /api/profile`

### 7.2 更新个人资料
- **接口**: `PUT /api/profile`
- **入参**:
  ```json
  {
    "username": "string",
    "email": "string",
    "phone": "string",
    "avatar": "string"
  }
  ```

### 7.3 修改密码
- **接口**: `PUT /api/profile/password`
- **入参**:
  ```json
  {
    "oldPassword": "string",
    "newPassword": "string"
  }
  ```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "errors": [
    {
      "field": "字段名",
      "message": "错误信息"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 权限要求

| 模块 | 查看 | 创建 | 编辑 | 删除 | 审核 |
|------|------|------|------|------|------|
| 用户管理 | MANAGER+ | ADMIN | ADMIN | ADMIN | - |
| 项目管理 | ALL | MANAGER+ | MANAGER+ | ADMIN | - |
| 团队管理 | ALL | MANAGER+ | MANAGER+ | ADMIN | - |
| 财务管理 | MANAGER+ | MANAGER+ | MANAGER+ | ADMIN | ADMIN |
| 报表查看 | ALL | - | - | - | - |

## HTTP状态码

- 200: 成功
- 201: 创建成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误
