package com.petroleum.management.dto;

import java.math.BigDecimal;

public class DashboardStatsDTO {
    private Long activeProjects;
    private Long totalTeams;
    private BigDecimal monthlyIncome;
    private Long totalUsers;

    // 构造函数
    public DashboardStatsDTO() {}

    public DashboardStatsDTO(Long activeProjects, Long totalTeams, BigDecimal monthlyIncome, Long totalUsers) {
        this.activeProjects = activeProjects;
        this.totalTeams = totalTeams;
        this.monthlyIncome = monthlyIncome;
        this.totalUsers = totalUsers;
    }

    // Getters and Setters
    public Long getActiveProjects() { return activeProjects; }
    public void setActiveProjects(Long activeProjects) { this.activeProjects = activeProjects; }

    public Long getTotalTeams() { return totalTeams; }
    public void setTotalTeams(Long totalTeams) { this.totalTeams = totalTeams; }

    public BigDecimal getMonthlyIncome() { return monthlyIncome; }
    public void setMonthlyIncome(BigDecimal monthlyIncome) { this.monthlyIncome = monthlyIncome; }

    public Long getTotalUsers() { return totalUsers; }
    public void setTotalUsers(Long totalUsers) { this.totalUsers = totalUsers; }
}
