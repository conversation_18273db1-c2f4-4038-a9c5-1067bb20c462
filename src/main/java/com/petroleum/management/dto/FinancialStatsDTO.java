package com.petroleum.management.dto;

import java.math.BigDecimal;

public class FinancialStatsDTO {
    private BigDecimal totalIncome;
    private BigDecimal totalExpense;
    private BigDecimal totalProfit;
    private Long pendingCount;

    // 构造函数
    public FinancialStatsDTO() {}

    public FinancialStatsDTO(BigDecimal totalIncome, BigDecimal totalExpense, BigDecimal totalProfit, Long pendingCount) {
        this.totalIncome = totalIncome;
        this.totalExpense = totalExpense;
        this.totalProfit = totalProfit;
        this.pendingCount = pendingCount;
    }

    // Getters and Setters
    public BigDecimal getTotalIncome() { return totalIncome; }
    public void setTotalIncome(BigDecimal totalIncome) { this.totalIncome = totalIncome; }

    public BigDecimal getTotalExpense() { return totalExpense; }
    public void setTotalExpense(BigDecimal totalExpense) { this.totalExpense = totalExpense; }

    public BigDecimal getTotalProfit() { return totalProfit; }
    public void setTotalProfit(BigDecimal totalProfit) { this.totalProfit = totalProfit; }

    public Long getPendingCount() { return pendingCount; }
    public void setPendingCount(Long pendingCount) { this.pendingCount = pendingCount; }
}
