package com.petroleum.management.service;

import com.petroleum.management.dto.TeamDTO;
import com.petroleum.management.dto.UserDTO;
import com.petroleum.management.entity.Team;
import com.petroleum.management.entity.User;
import com.petroleum.management.repository.TeamRepository;
import com.petroleum.management.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TeamService {
    
    @Autowired
    private TeamRepository teamRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private UserService userService;

    public Page<TeamDTO> getAllTeams(String keyword, Pageable pageable) {
        Page<Team> teamPage = teamRepository.findTeamsWithFilters(keyword, pageable);
        return teamPage.map(this::convertToDTO);
    }

    public TeamDTO getTeamById(Long id) {
        Team team = teamRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("团队未找到"));
        return convertToDTO(team);
    }

    public TeamDTO createTeam(TeamDTO teamDTO) {
        Team team = new Team();
        updateTeamFromDTO(team, teamDTO);
        
        Team savedTeam = teamRepository.save(team);
        return convertToDTO(savedTeam);
    }

    public TeamDTO updateTeam(Long id, TeamDTO teamDTO) {
        Team team = teamRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("团队未找到"));
        
        updateTeamFromDTO(team, teamDTO);
        Team savedTeam = teamRepository.save(team);
        return convertToDTO(savedTeam);
    }

    public void deleteTeam(Long id) {
        if (!teamRepository.existsById(id)) {
            throw new RuntimeException("团队未找到");
        }
        teamRepository.deleteById(id);
    }

    public List<UserDTO> getTeamMembers(Long teamId) {
        Team team = teamRepository.findById(teamId)
                .orElseThrow(() -> new RuntimeException("团队未找到"));
        
        return team.getMembers().stream()
                .map(user -> userService.convertToDTO(user))
                .collect(Collectors.toList());
    }

    private void updateTeamFromDTO(Team team, TeamDTO dto) {
        team.setName(dto.getName());
        team.setDescription(dto.getDescription());
        
        if (dto.getLeaderId() != null) {
            User leader = userRepository.findById(dto.getLeaderId())
                    .orElseThrow(() -> new RuntimeException("团队负责人未找到"));
            team.setLeader(leader);
        }
        
        if (dto.getMemberIds() != null && !dto.getMemberIds().isEmpty()) {
            Set<User> members = new HashSet<>();
            for (Long memberId : dto.getMemberIds()) {
                User member = userRepository.findById(memberId)
                        .orElseThrow(() -> new RuntimeException("团队成员未找到: " + memberId));
                members.add(member);
            }
            team.setMembers(members);
        }
    }

    private TeamDTO convertToDTO(Team team) {
        TeamDTO dto = new TeamDTO();
        dto.setId(team.getId());
        dto.setName(team.getName());
        dto.setDescription(team.getDescription());
        dto.setCreatedAt(team.getCreatedAt());
        dto.setUpdatedAt(team.getUpdatedAt());
        
        if (team.getLeader() != null) {
            dto.setLeaderId(team.getLeader().getId());
            dto.setLeaderName(team.getLeader().getRealName());
        }
        
        if (team.getMembers() != null) {
            dto.setMemberCount(team.getMembers().size());
            dto.setMemberIds(team.getMembers().stream()
                    .map(User::getId)
                    .collect(Collectors.toSet()));
            dto.setMembers(team.getMembers().stream()
                    .map(user -> userService.convertToDTO(user))
                    .collect(Collectors.toList()));
        } else {
            dto.setMemberCount(0);
        }
        
        return dto;
    }
}
