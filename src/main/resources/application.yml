spring:
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

server:
  port: 8080

jwt:
  secret: petroluemManagementSystemSecretKeyForHS512AlgorithmMustBeAtLeast512BitsLongToEnsureSecurityCompliance2024
  expiration: 86400000 # 24小时

logging:
  level:
    com.petroleum: debug
    org.springframework.security: debug